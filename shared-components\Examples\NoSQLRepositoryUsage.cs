using Amazon.DynamoDBv2.DataModel;
using Microsoft.Extensions.DependencyInjection;
using shared.Extension;
using shared.Models.Document;
using shared.Services;

namespace shared.Examples
{
    /// <summary>
    /// Example usage of the NoSQL abstraction layer.
    /// This file demonstrates how to use the new repository pattern with DynamoDB.
    /// </summary>
    public class NoSQLRepositoryUsage
    {
        /// <summary>
        /// Example model that extends BaseModel (which now extends DynamoDBModel).
        /// </summary>
        [DynamoDBTable("ExampleUser")]
        public class ExampleUser : BaseModel
        {
            public const string AccountIdUserIdIndex = "AccountId-UserId-index";

            [DynamoDBHashKey]
            [DynamoDBGlobalSecondaryIndexHashKey(AccountIdUserIdIndex)]
            public string AccountId { get; set; } = string.Empty;

            [DynamoDBRangeKey]
            [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdUserIdIndex)]
            public string UserId { get; set; } = string.Empty;

            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;

            /// <summary>
            /// Override static methods to provide type-specific implementations.
            /// </summary>
            public static new string? GetHashKeyPropertyName(string? indexName = null)
            {
                return DynamoDBModel.GetHashKeyPropertyName(typeof(ExampleUser), indexName);
            }

            public static new string? GetRangeKeyPropertyName(string? indexName = null)
            {
                return DynamoDBModel.GetRangeKeyPropertyName(typeof(ExampleUser), indexName);
            }

            public static new string GetTableName()
            {
                return DynamoDBModel.GetTableName(typeof(ExampleUser));
            }

            public override string GetSearchString()
            {
                return $"{Name} {Email}".ToLower();
            }
        }

        /// <summary>
        /// Example service that uses the NoSQL repository.
        /// </summary>
        public class ExampleUserService
        {
            private readonly INoSQLRepository<ExampleUser> _userRepository;

            public ExampleUserService(INoSQLRepository<ExampleUser> userRepository)
            {
                _userRepository = userRepository;
            }

            /// <summary>
            /// Creates a new user.
            /// </summary>
            public async Task<ExampleUser?> CreateUserAsync(string accountId, string userId, string name, string email)
            {
                var user = new ExampleUser
                {
                    AccountId = accountId,
                    UserId = userId,
                    Name = name,
                    Email = email
                };

                return await _userRepository.PutAsync(user);
            }

            /// <summary>
            /// Gets a user by account ID and user ID.
            /// </summary>
            public async Task<ExampleUser?> GetUserAsync(string accountId, string userId)
            {
                return await _userRepository.GetAsync(accountId, userId);
            }

            /// <summary>
            /// Gets a user using the secondary index.
            /// </summary>
            public async Task<ExampleUser?> GetUserByIndexAsync(string accountId, string userId)
            {
                return await _userRepository.GetByIndexAsync(accountId, userId, ExampleUser.AccountIdUserIdIndex);
            }

            /// <summary>
            /// Searches users by name or email.
            /// </summary>
            public async Task<List<ExampleUser>> SearchUsersAsync(string accountId, string searchText, int maxResults = 10)
            {
                var result = await _userRepository.SearchAsync(accountId, searchText, maxResults);
                return result.Entries;
            }

            /// <summary>
            /// Updates a user's name.
            /// </summary>
            public async Task<ExampleUser?> UpdateUserNameAsync(string accountId, string userId, string newName)
            {
                var user = await GetUserAsync(accountId, userId);
                if (user == null) return null;

                user.Name = newName;
                return await _userRepository.UpdateAsync(user, new List<string> { nameof(ExampleUser.Name) });
            }

            /// <summary>
            /// Deletes a user.
            /// </summary>
            public async Task<bool> DeleteUserAsync(string accountId, string userId)
            {
                return await _userRepository.DeleteAsync(accountId, userId);
            }

            /// <summary>
            /// Gets all users for an account.
            /// </summary>
            public async Task<List<ExampleUser>> GetAllUsersForAccountAsync(string accountId, int maxResults = 100)
            {
                var result = await _userRepository.QueryAsync(accountId, maxResults);
                return result.Entries;
            }

            /// <summary>
            /// Creates the table with all indexes if it doesn't exist.
            /// </summary>
            public async Task<bool> EnsureTableExistsAsync()
            {
                return await _userRepository.CreateTableAsync();
            }

            /// <summary>
            /// Validates that the table configuration matches the model.
            /// </summary>
            public async Task<TableValidationResult> ValidateTableConfigurationAsync()
            {
                return await _userRepository.ValidateTableAsync();
            }

            /// <summary>
            /// Checks if table exists and is properly configured.
            /// </summary>
            public async Task<bool> IsTableReadyAsync()
            {
                if (!await _userRepository.TableExistsAsync())
                    return false;

                var validation = await _userRepository.ValidateTableAsync();
                return validation.IsValid;
            }
        }

        /// <summary>
        /// Example of how to configure dependency injection.
        /// </summary>
        public static class ServiceConfiguration
        {
            /// <summary>
            /// Configures services in Program.cs or Startup.cs.
            /// </summary>
            public static void ConfigureServices(IServiceCollection services)
            {
                // Option 1: Add DynamoDB with repository (includes AWS services)
                services.AddDynamoDBWithRepository();

                // Option 2: Add just the repository (if AWS services are already configured)
                // services.AddDynamoDBRepository();

                // Option 3: Add repository for specific model type
                // services.AddDynamoDBRepository<ExampleUser>();

                // Option 4: Add with custom configuration
                // services.AddDynamoDBWithRepository(options =>
                // {
                //     options.AutoCreateTables = true;
                //     options.TableCreationTimeoutMinutes = 10;
                //     options.EnablePointInTimeRecovery = true;
                // });

                // Register your service
                services.AddScoped<ExampleUserService>();
            }
        }

        /// <summary>
        /// Example of how to use the service in a controller.
        /// </summary>
        public class ExampleController
        {
            private readonly ExampleUserService _userService;

            public ExampleController(ExampleUserService userService)
            {
                _userService = userService;
            }

            public async Task<ExampleUser?> CreateUser(string accountId, string name, string email)
            {
                var userId = Guid.NewGuid().ToString();
                return await _userService.CreateUserAsync(accountId, userId, name, email);
            }

            public async Task<ExampleUser?> GetUser(string accountId, string userId)
            {
                return await _userService.GetUserAsync(accountId, userId);
            }

            public async Task<List<ExampleUser>> SearchUsers(string accountId, string searchText)
            {
                return await _userService.SearchUsersAsync(accountId, searchText);
            }

            /// <summary>
            /// Ensures the table is created and properly configured.
            /// </summary>
            public async Task<bool> InitializeTable()
            {
                return await _userService.EnsureTableExistsAsync();
            }

            /// <summary>
            /// Validates the table configuration and returns any issues.
            /// </summary>
            public async Task<string> ValidateTableConfiguration()
            {
                var validation = await _userService.ValidateTableConfigurationAsync();

                if (validation.IsValid)
                {
                    return "Table configuration is valid.";
                }

                return $"Table validation failed:\n{string.Join("\n", validation.ValidationErrors)}";
            }
        }

        /// <summary>
        /// Example of table management operations.
        /// </summary>
        public class TableManagementExample
        {
            private readonly INoSQLRepository<ExampleUser> _repository;

            public TableManagementExample(INoSQLRepository<ExampleUser> repository)
            {
                _repository = repository;
            }

            /// <summary>
            /// Complete table setup and validation example.
            /// </summary>
            public async Task<string> SetupAndValidateTableAsync()
            {
                var results = new List<string>();

                // Check if table exists
                bool tableExists = await _repository.TableExistsAsync();
                results.Add($"Table exists: {tableExists}");

                if (!tableExists)
                {
                    // Create table with all indexes
                    results.Add("Creating table...");
                    bool created = await _repository.CreateTableAsync();
                    results.Add($"Table creation result: {created}");
                }

                // Validate table configuration
                results.Add("Validating table configuration...");
                var validation = await _repository.ValidateTableAsync();

                results.Add($"Table validation result: {validation.IsValid}");
                results.Add($"Table status: {validation.TableStatus}");

                if (!validation.IsValid)
                {
                    results.Add("Validation errors:");
                    results.AddRange(validation.ValidationErrors);
                }

                // Show primary key validation
                results.Add($"Primary key - Expected Hash: {validation.PrimaryKey.ExpectedHashKey}, Actual: {validation.PrimaryKey.ActualHashKey}");
                results.Add($"Primary key - Expected Range: {validation.PrimaryKey.ExpectedRangeKey}, Actual: {validation.PrimaryKey.ActualRangeKey}");

                // Show secondary index validation
                foreach (var index in validation.SecondaryIndexes)
                {
                    results.Add($"Index '{index.IndexName}' ({index.IndexType}): Valid={index.IsValid}, Exists={index.IndexExists}");
                    if (!index.IsValid)
                    {
                        results.AddRange(index.ValidationErrors.Select(e => $"  - {e}"));
                    }
                }

                return string.Join("\n", results);
            }
        }
    }
}
