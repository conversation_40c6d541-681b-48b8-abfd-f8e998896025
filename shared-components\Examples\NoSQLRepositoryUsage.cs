using Amazon.DynamoDBv2.DataModel;
using Microsoft.Extensions.DependencyInjection;
using shared.Extension;
using shared.Models.Document;
using shared.Services;

namespace shared.Examples
{
    /// <summary>
    /// Example usage of the NoSQL abstraction layer.
    /// This file demonstrates how to use the new repository pattern with DynamoDB.
    /// </summary>
    public class NoSQLRepositoryUsage
    {
        /// <summary>
        /// Example model that extends BaseModel (which now extends DynamoDBModel).
        /// </summary>
        [DynamoDBTable("ExampleUser")]
        public class ExampleUser : BaseModel
        {
            public const string AccountIdUserIdIndex = "AccountId-UserId-index";

            [DynamoDBHashKey]
            [DynamoDBGlobalSecondaryIndexHashKey(AccountIdUserIdIndex)]
            public string AccountId { get; set; } = string.Empty;

            [DynamoDBRangeKey]
            [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdUserIdIndex)]
            public string UserId { get; set; } = string.Empty;

            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;

            /// <summary>
            /// Override static methods to provide type-specific implementations.
            /// </summary>
            public static new string? GetHashKeyPropertyName(string? indexName = null)
            {
                return DynamoDBModel.GetHashKeyPropertyName(typeof(ExampleUser), indexName);
            }

            public static new string? GetRangeKeyPropertyName(string? indexName = null)
            {
                return DynamoDBModel.GetRangeKeyPropertyName(typeof(ExampleUser), indexName);
            }

            public static new string GetTableName()
            {
                return DynamoDBModel.GetTableName(typeof(ExampleUser));
            }

            public override string GetSearchString()
            {
                return $"{Name} {Email}".ToLower();
            }
        }

        /// <summary>
        /// Example service that uses the NoSQL repository.
        /// </summary>
        public class ExampleUserService
        {
            private readonly INoSQLRepository<ExampleUser> _userRepository;

            public ExampleUserService(INoSQLRepository<ExampleUser> userRepository)
            {
                _userRepository = userRepository;
            }

            /// <summary>
            /// Creates a new user.
            /// </summary>
            public async Task<ExampleUser?> CreateUserAsync(string accountId, string userId, string name, string email)
            {
                var user = new ExampleUser
                {
                    AccountId = accountId,
                    UserId = userId,
                    Name = name,
                    Email = email
                };

                return await _userRepository.PutAsync(user);
            }

            /// <summary>
            /// Gets a user by account ID and user ID.
            /// </summary>
            public async Task<ExampleUser?> GetUserAsync(string accountId, string userId)
            {
                return await _userRepository.GetAsync(accountId, userId);
            }

            /// <summary>
            /// Gets a user using the secondary index.
            /// </summary>
            public async Task<ExampleUser?> GetUserByIndexAsync(string accountId, string userId)
            {
                return await _userRepository.GetByIndexAsync(accountId, userId, ExampleUser.AccountIdUserIdIndex);
            }

            /// <summary>
            /// Searches users by name or email.
            /// </summary>
            public async Task<List<ExampleUser>> SearchUsersAsync(string accountId, string searchText, int maxResults = 10)
            {
                var result = await _userRepository.SearchAsync(accountId, searchText, maxResults);
                return result.Entries;
            }

            /// <summary>
            /// Updates a user's name.
            /// </summary>
            public async Task<ExampleUser?> UpdateUserNameAsync(string accountId, string userId, string newName)
            {
                var user = await GetUserAsync(accountId, userId);
                if (user == null) return null;

                user.Name = newName;
                return await _userRepository.UpdateAsync(user, new List<string> { nameof(ExampleUser.Name) });
            }

            /// <summary>
            /// Deletes a user.
            /// </summary>
            public async Task<bool> DeleteUserAsync(string accountId, string userId)
            {
                return await _userRepository.DeleteAsync(accountId, userId);
            }

            /// <summary>
            /// Gets all users for an account.
            /// </summary>
            public async Task<List<ExampleUser>> GetAllUsersForAccountAsync(string accountId, int maxResults = 100)
            {
                var result = await _userRepository.QueryAsync(accountId, maxResults);
                return result.Entries;
            }
        }

        /// <summary>
        /// Example of how to configure dependency injection.
        /// </summary>
        public static class ServiceConfiguration
        {
            /// <summary>
            /// Configures services in Program.cs or Startup.cs.
            /// </summary>
            public static void ConfigureServices(IServiceCollection services)
            {
                // Option 1: Add DynamoDB with repository (includes AWS services)
                services.AddDynamoDBWithRepository();

                // Option 2: Add just the repository (if AWS services are already configured)
                // services.AddDynamoDBRepository();

                // Option 3: Add repository for specific model type
                // services.AddDynamoDBRepository<ExampleUser>();

                // Option 4: Add with custom configuration
                // services.AddDynamoDBWithRepository(options =>
                // {
                //     options.AutoCreateTables = true;
                //     options.TableCreationTimeoutMinutes = 10;
                //     options.EnablePointInTimeRecovery = true;
                // });

                // Register your service
                services.AddScoped<ExampleUserService>();
            }
        }

        /// <summary>
        /// Example of how to use the service in a controller.
        /// </summary>
        public class ExampleController
        {
            private readonly ExampleUserService _userService;

            public ExampleController(ExampleUserService userService)
            {
                _userService = userService;
            }

            public async Task<ExampleUser?> CreateUser(string accountId, string name, string email)
            {
                var userId = Guid.NewGuid().ToString();
                return await _userService.CreateUserAsync(accountId, userId, name, email);
            }

            public async Task<ExampleUser?> GetUser(string accountId, string userId)
            {
                return await _userService.GetUserAsync(accountId, userId);
            }

            public async Task<List<ExampleUser>> SearchUsers(string accountId, string searchText)
            {
                return await _userService.SearchUsersAsync(accountId, searchText);
            }
        }
    }
}
