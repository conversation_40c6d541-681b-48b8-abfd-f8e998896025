# NoSQL Abstraction Layer

This document describes the NoSQL abstraction layer implemented in the shared-components project. The abstraction provides a generic interface for NoSQL database operations while maintaining backward compatibility with existing code.

## Architecture Overview

The abstraction layer consists of several key components:

### 1. NoSQLModel (Base Abstract Class)
- **Location**: `Models/Document/NoSQLModel.cs`
- **Purpose**: Provides the foundation for all NoSQL models with basic properties and abstract methods
- **Key Features**:
  - Common properties: `DataVersion`, `ModelVersion`, `LastChangeTimestamp`, `CreatedAt`, `SearchString`
  - Abstract methods for key resolution that must be implemented by derived classes
  - Utility methods for reflection-based property access

### 2. DynamoDBModel (Intermediate Class)
- **Location**: `Models/Document/DynamoDBModel.cs`
- **Purpose**: Extends NoSQLModel with DynamoDB-specific implementations
- **Key Features**:
  - Automatic key discovery using DynamoDB attributes (`DynamoDBHashKey`, `DynamoDBRangeKey`, etc.)
  - Support for secondary indexes (GSI and LSI)
  - Table name resolution from `DynamoDBTable` attribute
  - DynamoDB converter support

### 3. BaseModel (Backward Compatibility)
- **Location**: `Models/Document/BaseModel.cs`
- **Purpose**: Maintains backward compatibility with existing code
- **Key Features**:
  - Extends DynamoDBModel
  - Preserves existing method signatures (`GetHashKey()`, `GetRangeKey()`)
  - Static polymorphism support using the `new` keyword

### 4. INoSQLRepository Interface
- **Location**: `Services/INoSQLRepository.cs`
- **Purpose**: Defines the contract for NoSQL data operations
- **Key Features**:
  - Generic CRUD operations
  - Search and query capabilities
  - Secondary index support
  - Batch operations
  - **Note**: Table management operations are handled by INoSQLTableManager

### 5. INoSQLTableManager Interface
- **Location**: `Services/INoSQLTableManager.cs`
- **Purpose**: Defines the contract for NoSQL table management operations
- **Key Features**:
  - Table creation with automatic secondary index detection
  - Table validation and schema verification
  - Table deletion
  - Schema information retrieval

### 6. DynamoDBRepository Implementation
- **Location**: `Services/Implementation/DynamoDBRepository.cs`
- **Purpose**: Provides DynamoDB-specific implementation of INoSQLRepository
- **Key Features**:
  - Full implementation of all data operation methods
  - Automatic key resolution using model metadata
  - Error handling and null safety
  - Pagination support
  - **Dependency**: Only requires IDynamoDBContext

### 7. DynamoDBTableManager Implementation
- **Location**: `Services/Implementation/DynamoDBTableManager.cs`
- **Purpose**: Provides DynamoDB-specific implementation of INoSQLTableManager
- **Key Features**:
  - Automatic secondary index detection and creation
  - Comprehensive table configuration validation
  - Schema information retrieval
  - Table lifecycle management
  - **Dependency**: Requires IAmazonDynamoDB

### 8. Dependency Injection Extensions
- **Location**: `Extension/NoSQLServiceExtensions.cs`
- **Purpose**: Simplifies service registration in DI container
- **Key Features**:
  - Separate registration for repository and table manager
  - Multiple registration options (combined or individual)
  - Configuration support
  - AWS service integration

## Usage Examples

### 1. Basic Model Definition

```csharp
[DynamoDBTable("MyTable")]
public class MyModel : BaseModel
{
    public const string AccountIdIdIndex = "AccountId-Id-index";

    [DynamoDBHashKey]
    [DynamoDBGlobalSecondaryIndexHashKey(AccountIdIdIndex)]
    public string AccountId { get; set; } = string.Empty;

    [DynamoDBRangeKey]
    [DynamoDBGlobalSecondaryIndexRangeKey(AccountIdIdIndex)]
    public string Id { get; set; } = string.Empty;

    public string Name { get; set; } = string.Empty;

    // Override static methods for proper key resolution
    public static new string? GetHashKeyPropertyName(string? indexName = null)
    {
        return DynamoDBModel.GetHashKeyPropertyName(typeof(MyModel), indexName);
    }

    public static new string? GetRangeKeyPropertyName(string? indexName = null)
    {
        return DynamoDBModel.GetRangeKeyPropertyName(typeof(MyModel), indexName);
    }

    public static new string GetTableName()
    {
        return DynamoDBModel.GetTableName(typeof(MyModel));
    }

    public override string GetSearchString()
    {
        return Name.ToLower();
    }
}
```

### 2. Service Registration

```csharp
// In Program.cs or Startup.cs
public void ConfigureServices(IServiceCollection services)
{
    // Option 1: Complete setup with both repository and table manager
    services.AddDynamoDBWithServices();

    // Option 2: Only data operations (repository)
    services.AddDynamoDBWithRepository();

    // Option 3: Only table management
    services.AddDynamoDBWithTableManager();

    // Option 4: Separate registration (if AWS services already configured)
    services.AddDynamoDBRepository();
    services.AddDynamoDBTableManager();

    // Option 5: Specific model types
    services.AddDynamoDBRepository<MyModel>();
    services.AddDynamoDBTableManager<MyModel>();

    // Option 6: With configuration
    services.AddDynamoDBWithServices(options =>
    {
        options.AutoCreateTables = true;
        options.TableCreationTimeoutMinutes = 10;
    });
}
```

### 3. Data Operations (Repository Usage)

```csharp
public class MyDataService
{
    private readonly INoSQLRepository<MyModel> _repository;

    public MyDataService(INoSQLRepository<MyModel> repository)
    {
        _repository = repository;
    }

    // Create
    public async Task<MyModel?> CreateAsync(MyModel model)
    {
        return await _repository.PutAsync(model);
    }

    // Read
    public async Task<MyModel?> GetAsync(string accountId, string id)
    {
        return await _repository.GetAsync(accountId, id);
    }

    // Read by index
    public async Task<MyModel?> GetByIndexAsync(string accountId, string id)
    {
        return await _repository.GetByIndexAsync(accountId, id, MyModel.AccountIdIdIndex);
    }

    // Update
    public async Task<MyModel?> UpdateNameAsync(MyModel model, string newName)
    {
        model.Name = newName;
        return await _repository.UpdateAsync(model, new List<string> { nameof(MyModel.Name) });
    }

    // Delete
    public async Task<bool> DeleteAsync(string accountId, string id)
    {
        return await _repository.DeleteAsync(accountId, id);
    }

    // Search
    public async Task<List<MyModel>> SearchAsync(string accountId, string searchText)
    {
        var result = await _repository.SearchAsync(accountId, searchText, 10);
        return result.Entries;
    }

    // Query
    public async Task<List<MyModel>> GetAllForAccountAsync(string accountId)
    {
        var result = await _repository.QueryAsync(accountId, 100);
        return result.Entries;
    }
}
```

### 4. Table Management

```csharp
public class TableManagementService
{
    private readonly INoSQLRepository<MyModel> _repository;

    public TableManagementService(INoSQLRepository<MyModel> repository)
    {
        _repository = repository;
    }

    // Create table with all indexes
    public async Task<bool> CreateTableAsync()
    {
        return await _repository.CreateTableAsync();
    }

    // Validate table configuration
    public async Task<TableValidationResult> ValidateTableAsync()
    {
        return await _repository.ValidateTableAsync();
    }

    // Check if table exists
    public async Task<bool> TableExistsAsync()
    {
        return await _repository.TableExistsAsync();
    }

    // Complete setup and validation
    public async Task<string> SetupTableAsync()
    {
        // Check if table exists
        if (!await _repository.TableExistsAsync())
        {
            // Create table with all primary keys and secondary indexes
            bool created = await _repository.CreateTableAsync();
            if (!created)
                return "Failed to create table";
        }

        // Validate configuration
        var validation = await _repository.ValidateTableAsync();
        if (!validation.IsValid)
        {
            return $"Table validation failed: {string.Join(", ", validation.ValidationErrors)}";
        }

        return "Table is ready";
    }
}
```

## Migration from SuperController

The new abstraction layer is designed to replace the methods in SuperController. Here's the mapping:

| SuperController Method | Repository Method |
|------------------------|-------------------|
| `PutDBEntry<T>()` | `PutAsync()` |
| `GetDBEntry<T>()` | `GetAsync()` |
| `GetDBEntry<T>(hashKey, rangeKey, indexName, propertyName)` | `GetByIndexAsync()` |
| `UpdateDBEntry<T>()` | `UpdateAsync()` |
| `DeleteDBEntry<T>()` | `DeleteAsync()` |
| `SearchDBEntries<T>()` | `SearchAsync()` |
| `BatchGetDBEntry<T>()` | `BatchGetAsync()` |

## Advanced Table Management Features

### Automatic Secondary Index Creation

The `CreateTableAsync()` method automatically detects and creates all secondary indexes defined in your model:

- **Global Secondary Indexes (GSI)**: Detected from `DynamoDBGlobalSecondaryIndexHashKey` and `DynamoDBGlobalSecondaryIndexRangeKey` attributes
- **Local Secondary Indexes (LSI)**: Detected from `DynamoDBLocalSecondaryIndexRangeKey` attributes
- **Attribute Definitions**: Automatically added for all key attributes
- **Projection**: All indexes are created with `ProjectionType.ALL` for maximum flexibility

### Table Configuration Validation

The `ValidateTableAsync()` method provides comprehensive validation:

```csharp
var validation = await repository.ValidateTableAsync();

if (!validation.IsValid)
{
    Console.WriteLine("Table validation failed:");
    foreach (var error in validation.ValidationErrors)
    {
        Console.WriteLine($"- {error}");
    }

    // Check primary key issues
    if (!validation.PrimaryKey.IsValid)
    {
        Console.WriteLine($"Primary key issues: {string.Join(", ", validation.PrimaryKey.ValidationErrors)}");
    }

    // Check secondary index issues
    foreach (var index in validation.SecondaryIndexes.Where(i => !i.IsValid))
    {
        Console.WriteLine($"Index '{index.IndexName}' issues: {string.Join(", ", index.ValidationErrors)}");
    }
}
```

### Validation Features

- **Primary Key Validation**: Checks hash and range key names match model expectations
- **Secondary Index Validation**: Verifies all expected indexes exist with correct key schemas
- **Unexpected Index Detection**: Identifies indexes in the table that aren't defined in the model
- **Index Status Checking**: Reports the current status of each index
- **Detailed Error Reporting**: Provides specific information about each validation failure

## Key Benefits

1. **Abstraction**: Clean separation between business logic and data access
2. **Testability**: Easy to mock for unit testing
3. **Flexibility**: Can support multiple NoSQL providers
4. **Type Safety**: Generic interface with compile-time type checking
5. **Automatic Key Resolution**: No need to manually specify keys in method calls
6. **Automatic Index Management**: Secondary indexes are created automatically
7. **Configuration Validation**: Ensures table matches model expectations
8. **Backward Compatibility**: Existing code continues to work
9. **Dependency Injection**: First-class DI support

## Configuration Options

The `DynamoDBRepositoryOptions` class provides several configuration options:

- `TableCreationTimeoutMinutes`: Timeout for table creation operations
- `AutoCreateTables`: Automatically create tables if they don't exist
- `DefaultBillingMode`: Default billing mode for new tables
- `EnablePointInTimeRecovery`: Enable PITR for new tables
- `DefaultReadCapacityUnits`: Default RCU for provisioned tables
- `DefaultWriteCapacityUnits`: Default WCU for provisioned tables

## Best Practices

### Model Definition
1. Always override the static key resolution methods in your model classes
2. Use meaningful index names as constants in your model classes
3. Implement `GetSearchString()` for models that need text search
4. Define all secondary indexes using appropriate DynamoDB attributes

### Repository Usage
5. Use the repository pattern in your services rather than direct DynamoDB access
6. Register repositories in DI container for proper lifecycle management
7. Handle null returns from repository methods appropriately
8. Use batch operations for multiple item operations when possible

### Table Management
9. Always validate table configuration in production environments
10. Use `CreateTableAsync()` during application startup or deployment
11. Monitor table validation results and alert on configuration drift
12. Consider using the `AutoCreateTables` option in development environments only
13. Validate tables after any model changes or deployments

### Example Startup Configuration
```csharp
public async Task ConfigureTablesAsync(IServiceProvider serviceProvider)
{
    var userRepository = serviceProvider.GetRequiredService<INoSQLRepository<User>>();
    var agentRepository = serviceProvider.GetRequiredService<INoSQLRepository<Agent>>();

    // Ensure tables exist
    await userRepository.CreateTableAsync();
    await agentRepository.CreateTableAsync();

    // Validate configurations
    var userValidation = await userRepository.ValidateTableAsync();
    var agentValidation = await agentRepository.ValidateTableAsync();

    if (!userValidation.IsValid || !agentValidation.IsValid)
    {
        // Log validation errors and potentially fail startup
        throw new InvalidOperationException("Table configuration validation failed");
    }
}
```

## Future Enhancements

The abstraction layer is designed to be extensible. Future enhancements could include:

- Support for other NoSQL databases (MongoDB, CosmosDB, etc.)
- Advanced query builders
- Caching layer integration
- Audit logging
- Performance monitoring
- Schema migration tools
